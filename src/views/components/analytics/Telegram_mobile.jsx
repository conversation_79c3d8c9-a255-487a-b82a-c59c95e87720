import React, { useState } from "react";
import siteConstant from "../../../helpers/constant/siteConstant";

function Telegram_mobile() {
  const [selectedCountry, setSelectedCountry] = useState("India");
  const [phoneNumber, setPhoneNumber] = useState("");

  // Country data with codes and flags
  const countryData = {
    Afghanistan: { code: "+93", flag: "🇦🇫" },
    Albania: { code: "+355", flag: "🇦🇱" },
    Algeria: { code: "+213", flag: "🇩🇿" },
    Argentina: { code: "+54", flag: "🇦🇷" },
    Australia: { code: "+61", flag: "🇦🇺" },
    Austria: { code: "+43", flag: "🇦🇹" },
    Bangladesh: { code: "+880", flag: "🇧🇩" },
    Belgium: { code: "+32", flag: "🇧🇪" },
    Brazil: { code: "+55", flag: "🇧🇷" },
    Canada: { code: "+1", flag: "🇨🇦" },
    China: { code: "+86", flag: "🇨🇳" },
    Denmark: { code: "+45", flag: "🇩🇰" },
    Egypt: { code: "+20", flag: "🇪🇬" },
    Finland: { code: "+358", flag: "🇫🇮" },
    France: { code: "+33", flag: "🇫🇷" },
    Germany: { code: "+49", flag: "🇩🇪" },
    Greece: { code: "+30", flag: "🇬🇷" },
    India: { code: "+91", flag: "🇮🇳" },
    Indonesia: { code: "+62", flag: "🇮🇩" },
    Iran: { code: "+98", flag: "🇮🇷" },
    Iraq: { code: "+964", flag: "🇮🇶" },
    Ireland: { code: "+353", flag: "🇮🇪" },
    Israel: { code: "+972", flag: "🇮🇱" },
    Italy: { code: "+39", flag: "🇮🇹" },
    Japan: { code: "+81", flag: "🇯🇵" },
    Jordan: { code: "+962", flag: "🇯🇴" },
    Kenya: { code: "+254", flag: "🇰🇪" },
    Kuwait: { code: "+965", flag: "��" },
    Malaysia: { code: "+60", flag: "🇲🇾" },
    Mexico: { code: "+52", flag: "🇲🇽" },
    Netherlands: { code: "+31", flag: "🇳🇱" },
    "New Zealand": { code: "+64", flag: "🇳🇿" },
    Nigeria: { code: "+234", flag: "🇳🇬" },
    Norway: { code: "+47", flag: "🇳�" },
    Pakistan: { code: "+92", flag: "🇵🇰" },
    Philippines: { code: "+63", flag: "🇵🇭" },
    Poland: { code: "+48", flag: "🇵🇱" },
    Portugal: { code: "+351", flag: "🇵🇹" },
    Qatar: { code: "+974", flag: "🇶🇦" },
    Russia: { code: "+7", flag: "🇷🇺" },
    "Saudi Arabia": { code: "+966", flag: "🇸🇦" },
    Singapore: { code: "+65", flag: "🇸🇬" },
    "South Africa": { code: "+27", flag: "�🇦" },
    "South Korea": { code: "+82", flag: "🇰🇷" },
    Spain: { code: "+34", flag: "🇪🇸" },
    "Sri Lanka": { code: "+94", flag: "🇱🇰" },
    Sweden: { code: "+46", flag: "🇸🇪" },
    Switzerland: { code: "+41", flag: "🇨🇭" },
    Thailand: { code: "+66", flag: "🇹🇭" },
    Turkey: { code: "+90", flag: "🇹🇷" },
    UAE: { code: "+971", flag: "🇦🇪" },
    UK: { code: "+44", flag: "🇬🇧" },
    Ukraine: { code: "+380", flag: "🇺🇦" },
    USA: { code: "+1", flag: "🇺🇸" },
    Vietnam: { code: "+84", flag: "🇻🇳" },
  };

  // Handle country change
  const handleCountryChange = (e) => {
    setSelectedCountry(e.target.value);
    setPhoneNumber(""); // Clear phone number when country changes
  };

  // Handle phone number input - only allow numbers
  const handlePhoneNumberChange = (e) => {
    const value = e.target.value;
    const numbersOnly = value.replace(/[^0-9]/g, ""); // Remove non-numeric characters
    setPhoneNumber(numbersOnly);
  };

  return (
    <div className="min-h-[500px] bg-white flex flex-col items-center px-6 py-8 font-Ubuntu">
      {/* Telegram Logo */}
      <div className="">
        <div className="w-24 h-24 bg-[#0088cc] rounded-full flex items-center justify-center">
          <img
            src={siteConstant.SOCIAL_ICONS.TELEGRAM}
            alt=""
            className="h-14 w-14"
          />
        </div>
      </div>

      {/* Title */}
      <h1 className="text-2xl font-medium text-gray-900 my-4">Telegram</h1>

      {/* Subtitle */}
      <p className="text-gray-500 text-center mb-12 px-4 leading-relaxed">
        Please confirm your country code and enter your phone number.
      </p>

      {/* Country Selection */}
      <div className="w-full max-w-sm mb-6 ">
        <label className="block text-gray-600 text-sm mb-2">Country</label>
        <div className="relative">
          <select
            className="w-full h-12 px-4 py-3 border border-gray-200 rounded-lg appearance-none bg-white focus:outline-none focus:border-blue-400 pr-10"
            value={selectedCountry}
            onChange={handleCountryChange}
          >
            {Object.keys(countryData).map((country) => (
              <option key={country} value={country}>
                {countryData[country].flag} {country}
              </option>
            ))}
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Mobile Number Input */}
      <div className="w-full max-w-sm mb-12">
        <label className="block text-gray-600 text-sm mb-2">
          Mobile Number
        </label>
        <div className="flex">
          <div className="flex items-center px-3 py-3 border border-gray-200 border-r-0 rounded-l-lg bg-gray-50">
            <span className="text-sm">
              {countryData[selectedCountry].flag}{" "}
              {countryData[selectedCountry].code}
            </span>
          </div>
          <input
            type="tel"
            placeholder="Enter Mobile Number"
            className="flex-1 px-4 py-3 border border-gray-200 rounded-r-lg focus:outline-none focus:border-blue-400"
            value={phoneNumber}
            onChange={handlePhoneNumberChange}
          />
        </div>
      </div>

      {/* Send OTP Button */}
      <button className="w-full max-w-sm bg-[#0088cc] text-white py-4 rounded-lg font-medium text-lg ">
        Send OTP
      </button>
    </div>
  );
}

export default Telegram_mobile;
